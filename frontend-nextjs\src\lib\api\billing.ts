// Billing API client functions for medical clinic system
// Handles bills, payments, and financial operations with comprehensive error handling

import { Bill, Payment, BillItem, PayloadResponse } from '@/types/clinic';
import {
  handleAPIError,
  handleNetworkError,
  retryWithBackoff,
  BillingError
} from '@/lib/billing-error-handler';

// API base URL - using relative paths for Next.js API routes
const API_BASE = '/api';

// Enhanced error handling utility
export class BillingAPIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'BillingAPIError';
  }
}

// Enhanced API request handler with comprehensive error handling and retry logic
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {},
  retryOptions?: { maxRetries?: number; context?: string }
): Promise<T> {
  const url = `${API_BASE}${endpoint}`;

  const defaultHeaders = {
    'Content-Type': 'application/json',
  };

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  const makeRequest = async (): Promise<T> => {
    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          errorData = {
            error: `HTTP ${response.status}: ${response.statusText}`,
            code: `HTTP_${response.status}`,
            message: response.statusText
          };
        }

        const apiError = new BillingAPIError(
          errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData.code || `HTTP_${response.status}`,
          errorData.details
        );

        // Handle the error through the error handler
        handleAPIError(errorData, {
          context: retryOptions?.context || 'API Request',
          showToast: false // Don't show toast here, let the calling function decide
        });

        throw apiError;
      }

      return await response.json();
    } catch (error) {
      if (error instanceof BillingAPIError) {
        throw error;
      }

      // Handle network errors
      const networkError = new BillingAPIError(
        error instanceof Error ? error.message : 'Network error occurred',
        0,
        'NETWORK_ERROR',
        error
      );

      handleNetworkError(error, {
        context: retryOptions?.context || 'API Request',
        showToast: false
      });

      throw networkError;
    }
  };

  // Use retry logic for GET requests and other idempotent operations
  const isIdempotent = !options.method || ['GET', 'HEAD', 'OPTIONS'].includes(options.method.toUpperCase());

  if (isIdempotent && retryOptions?.maxRetries) {
    return retryWithBackoff(
      makeRequest,
      retryOptions.maxRetries,
      1000,
      retryOptions.context
    );
  }

  return makeRequest();
}

// Bill API Functions
export const billsAPI = {
  /**
   * Fetch all bills with optional filtering and pagination
   */
  async fetchBills(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    patientId?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<PayloadResponse<Bill>> {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.patientId) searchParams.append('patient', params.patientId);
    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);

    const queryString = searchParams.toString();
    const endpoint = `/bills${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest<PayloadResponse<Bill>>(
      endpoint,
      {},
      { maxRetries: 3, context: 'Fetch Bills' }
    );
  },

  /**
   * Fetch a specific bill by ID
   */
  async fetchBill(id: string): Promise<Bill> {
    return apiRequest<Bill>(`/bills/${id}`);
  },

  /**
   * Create a new bill
   */
  async createBill(billData: {
    patient: string;
    appointment?: string;
    treatment?: string;
    billType: 'treatment' | 'consultation' | 'deposit' | 'additional';
    subtotal: number;
    discountAmount?: number;
    taxAmount?: number;
    totalAmount: number;
    description: string;
    notes?: string;
    dueDate: string;
    items?: Array<{
      itemType: 'treatment' | 'consultation' | 'material' | 'service';
      itemName: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      discountRate?: number;
    }>;
  }): Promise<Bill> {
    return apiRequest<Bill>('/bills', {
      method: 'POST',
      body: JSON.stringify(billData),
    });
  },

  /**
   * Update an existing bill
   */
  async updateBill(id: string, updateData: Partial<Bill>): Promise<Bill> {
    return apiRequest<Bill>(`/bills/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData),
    });
  },

  /**
   * Delete a bill
   */
  async deleteBill(id: string): Promise<void> {
    return apiRequest<void>(`/bills/${id}`, {
      method: 'DELETE',
    });
  },

  /**
   * Generate bill from appointment
   */
  async generateFromAppointment(appointmentId: string, billType: string = 'treatment'): Promise<Bill> {
    return apiRequest<Bill>('/bills/generate-from-appointment', {
      method: 'POST',
      body: JSON.stringify({ appointmentId, billType }),
    });
  },

  /**
   * Check if appointment already has a bill
   */
  async checkAppointmentBill(appointmentId: string): Promise<{ hasBill: boolean; bill?: Bill }> {
    try {
      const response = await billsAPI.fetchBills({
        limit: 1,
        // Note: This would need backend support for filtering by appointment
        // For now, we'll fetch and filter client-side in components
      });

      const bill = response.docs.find(bill =>
        typeof bill.appointment === 'object' && bill.appointment?.id === appointmentId
      );

      return {
        hasBill: !!bill,
        bill: bill || undefined,
      };
    } catch (error) {
      console.error('Failed to check appointment bill:', error);
      return { hasBill: false };
    }
  },
};

// Payment API Functions
export const paymentsAPI = {
  /**
   * Fetch all payments with optional filtering
   */
  async fetchPayments(params?: {
    page?: number;
    limit?: number;
    billId?: string;
    patientId?: string;
    paymentMethod?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<PayloadResponse<Payment>> {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.billId) searchParams.append('bill', params.billId);
    if (params?.patientId) searchParams.append('patient', params.patientId);
    if (params?.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);
    if (params?.status) searchParams.append('paymentStatus', params.status);
    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);

    const queryString = searchParams.toString();
    const endpoint = `/payments${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest<PayloadResponse<Payment>>(endpoint);
  },

  /**
   * Fetch a specific payment by ID
   */
  async fetchPayment(id: string): Promise<Payment> {
    return apiRequest<Payment>(`/payments/${id}`);
  },

  /**
   * Process a new payment
   */
  async processPayment(paymentData: {
    bill: string;
    patient: string;
    amount: number;
    paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';
    transactionId?: string;
    notes?: string;
  }): Promise<Payment> {
    return apiRequest<Payment>('/payments', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  },

  /**
   * Update payment status
   */
  async updatePayment(id: string, updateData: Partial<Payment>): Promise<Payment> {
    return apiRequest<Payment>(`/payments/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData),
    });
  },

  /**
   * Process refund
   */
  async processRefund(paymentId: string, refundData: {
    amount: number;
    reason: string;
    notes?: string;
  }): Promise<Payment> {
    return apiRequest<Payment>(`/payments/${paymentId}/refund`, {
      method: 'POST',
      body: JSON.stringify(refundData),
    });
  },
};

// Financial reporting API functions
export const reportsAPI = {
  /**
   * Get daily revenue report
   */
  async getDailyRevenue(date: string): Promise<{
    date: string;
    totalRevenue: number;
    paymentCount: number;
    paymentMethods: Record<string, { amount: number; count: number }>;
  }> {
    return apiRequest(`/reports/daily-revenue?date=${date}`);
  },

  /**
   * Get monthly revenue report
   */
  async getMonthlyRevenue(year: number, month: number): Promise<{
    year: number;
    month: number;
    totalRevenue: number;
    dailyBreakdown: Array<{ date: string; revenue: number }>;
  }> {
    return apiRequest(`/reports/monthly-revenue?year=${year}&month=${month}`);
  },

  /**
   * Get outstanding balances report
   */
  async getOutstandingBalances(): Promise<{
    totalOutstanding: number;
    overdueAmount: number;
    billsCount: number;
    overdueBillsCount: number;
    bills: Array<{
      id: string;
      billNumber: string;
      patient: string;
      amount: number;
      dueDate: string;
      daysOverdue: number;
    }>;
  }> {
    return apiRequest('/reports/outstanding-balances');
  },
};

// Deposit API functions
export const depositsAPI = {
  /**
   * Create a new deposit
   */
  async createDeposit(depositData: {
    patient: string;
    appointment?: string;
    treatment?: string;
    depositType: 'treatment' | 'appointment' | 'material';
    amount: number;
    purpose: string;
    notes?: string;
    expiryDate?: string;
  }): Promise<any> {
    return apiRequest('/deposits', {
      method: 'POST',
      body: JSON.stringify(depositData),
    });
  },

  /**
   * Get deposits with filtering and pagination
   */
  async getDeposits(params?: {
    page?: number;
    limit?: number;
    patient?: string;
    status?: string;
    depositType?: string;
  }): Promise<PayloadResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.patient) searchParams.set('where[patient][equals]', params.patient);
    if (params?.status) searchParams.set('where[status][equals]', params.status);
    if (params?.depositType) searchParams.set('where[depositType][equals]', params.depositType);

    return apiRequest(`/deposits?${searchParams.toString()}`);
  },

  /**
   * Get deposit by ID
   */
  async getDepositById(id: string): Promise<any> {
    return apiRequest(`/deposits/${id}`);
  },

  /**
   * Update deposit
   */
  async updateDeposit(id: string, updateData: {
    status?: string;
    usedAmount?: number;
    notes?: string;
  }): Promise<any> {
    return apiRequest(`/deposits/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData),
    });
  },

  /**
   * Apply deposit to bill
   */
  async applyToBill(depositId: string, billId: string, amount: number): Promise<any> {
    return apiRequest('/deposits/apply-to-bill', {
      method: 'POST',
      body: JSON.stringify({
        depositId,
        billId,
        amount,
      }),
    });
  },

  /**
   * Process deposit refund
   */
  async processRefund(depositId: string, refundAmount: number, refundReason: string, refundMethod: string = 'cash'): Promise<any> {
    return apiRequest('/deposits/refund', {
      method: 'POST',
      body: JSON.stringify({
        depositId,
        refundAmount,
        refundReason,
        refundMethod,
      }),
    });
  },
};

// Receipt API functions
export const receiptsAPI = {
  /**
   * Generate receipt for payment
   */
  async generateReceipt(paymentId: string): Promise<any> {
    return apiRequest(`/payments/${paymentId}/receipt`);
  },

  /**
   * Regenerate receipt number (admin only)
   */
  async regenerateReceipt(paymentId: string): Promise<any> {
    return apiRequest(`/payments/${paymentId}/receipt`, {
      method: 'POST',
    });
  },
};

// Financial reporting API functions
export const reportsAPI = {
  /**
   * Generate financial report
   */
  async getFinancialReport(params?: {
    startDate?: string;
    endDate?: string;
    type?: 'summary' | 'detailed';
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);
    if (params?.type) searchParams.set('type', params.type);

    return apiRequest(`/reports/financial?${searchParams.toString()}`);
  },
};

// Export the error class for use in components
export { BillingAPIError };

// Export utility functions
export const billingUtils = {
  /**
   * Format currency amount for display
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  },

  /**
   * Calculate bill total with discounts and taxes
   */
  calculateBillTotal(subtotal: number, discountAmount: number = 0, taxAmount: number = 0): number {
    return subtotal + taxAmount - discountAmount;
  },

  /**
   * Get payment method display name
   */
  getPaymentMethodName(method: string): string {
    const methods: Record<string, string> = {
      cash: '现金',
      card: '银行卡',
      wechat: '微信支付',
      alipay: '支付宝',
      transfer: '银行转账',
      deposit: '押金抵扣',
      installment: '分期付款',
    };
    return methods[method] || method;
  },

  /**
   * Get bill status display name
   */
  getBillStatusName(status: string): string {
    const statuses: Record<string, string> = {
      draft: '草稿',
      sent: '已发送',
      confirmed: '已确认',
      paid: '已支付',
      cancelled: '已取消',
    };
    return statuses[status] || status;
  },

  /**
   * Get payment status display name
   */
  getPaymentStatusName(status: string): string {
    const statuses: Record<string, string> = {
      pending: '待处理',
      completed: '已完成',
      failed: '失败',
      refunded: '已退款',
    };
    return statuses[status] || status;
  },

  /**
   * Get deposit status display name
   */
  getDepositStatusName(status: string): string {
    const statuses: Record<string, string> = {
      active: '有效',
      used: '已使用',
      refunded: '已退还',
      expired: '已过期',
    };
    return statuses[status] || status;
  },

  /**
   * Validate payment amount against bill balance
   */
  validatePaymentAmount(amount: number, billBalance: number): boolean {
    return amount > 0 && amount <= billBalance;
  },
};
